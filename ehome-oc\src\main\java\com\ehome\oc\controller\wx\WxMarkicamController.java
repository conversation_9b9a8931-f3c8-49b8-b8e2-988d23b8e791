package com.ehome.oc.controller.wx;

import com.alibaba.fastjson.JSONObject;
import com.ehome.common.core.controller.BaseWxController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.core.page.TableDataInfo;
import com.ehome.common.utils.sql.EasySQL;
import com.ehome.jfinal.utils.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.web.bind.annotation.*;

/**
 * 微信端Markicam数据接口
 * 
 * <AUTHOR>
 * @date 2025-08-08
 */
@RestController
@RequestMapping("/api/wx/markicam")
public class WxMarkicamController extends BaseWxController {

    /**
     * 获取公示的照片视频列表（仅返回is_public=1的数据）
     */
    @PostMapping("/moments")
    @ResponseBody
    public AjaxResult getMoments() {
        try {
            // 检查用户登录状态
            if (getCurrentUser() == null) {
                return AjaxResult.error("用户未登录");
            }
            
            JSONObject params = getParams();
            String communityId = getCurrentUser().getCommunityId();
            
            if (communityId == null) {
                return AjaxResult.error("未找到社区信息");
            }

            EasySQL sql = new EasySQL("from eh_markicam_moment m " +
                "LEFT JOIN eh_markicam_team t ON m.team_id = t.team_id AND m.community_id = t.community_id " +
                "LEFT JOIN eh_markicam_member mem ON m.uid = mem.uid AND m.community_id = mem.community_id " +
                "WHERE m.community_id = '" + communityId + "' AND m.is_deleted = 0 AND m.is_public = 1");

            // 添加查询条件
            sql.append(params.getInteger("team_id"), "AND m.team_id = ?");
            sql.append(params.getInteger("moment_type"), "AND m.moment_type = ?");
            sql.append(params.getString("start_time"), "AND m.post_time_str >= ?");
            sql.append(params.getString("end_time"), "AND m.post_time_str <= ?");

            sql.append("ORDER BY m.post_time DESC");

            Page<Record> paginate = Db.paginate(
                params.getIntValue("pageNum", 1),
                params.getIntValue("pageSize", 10),
                "SELECT m.*, t.team_name, mem.nickname",
                sql.getSQL(),
                sql.getParams()
            );

            return AjaxResult.success(getDataTable(paginate));
        } catch (Exception e) {
            logger.error("获取照片视频列表失败", e);
            return AjaxResult.error("获取列表失败：" + e.getMessage());
        }
    }

    /**
     * GET方式获取公示的照片视频列表
     */
    @GetMapping("/moments")
    @ResponseBody
    public AjaxResult getMomentsGet(
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            @RequestParam(value = "team_id", required = false) Integer teamId,
            @RequestParam(value = "moment_type", required = false) Integer momentType,
            @RequestParam(value = "start_time", required = false) String startTime,
            @RequestParam(value = "end_time", required = false) String endTime) {
        try {
            // 检查用户登录状态
            if (getCurrentUser() == null) {
                return AjaxResult.error("用户未登录");
            }
            
            String communityId = getCurrentUser().getCommunityId();
            
            if (communityId == null) {
                return AjaxResult.error("未找到社区信息");
            }

            EasySQL sql = new EasySQL("from eh_markicam_moment m " +
                "LEFT JOIN eh_markicam_team t ON m.team_id = t.team_id AND m.community_id = t.community_id " +
                "LEFT JOIN eh_markicam_member mem ON m.uid = mem.uid AND m.community_id = mem.community_id " +
                "WHERE m.community_id = '" + communityId + "' AND m.is_deleted = 0 AND m.is_public = 1");

            // 添加查询条件
            sql.append(teamId, "AND m.team_id = ?");
            sql.append(momentType, "AND m.moment_type = ?");
            sql.append(startTime, "AND m.post_time_str >= ?");
            sql.append(endTime, "AND m.post_time_str <= ?");

            sql.append("ORDER BY m.post_time DESC");

            Page<Record> paginate = Db.paginate(
                pageNum,
                pageSize,
                "SELECT m.*, t.team_name, mem.nickname",
                sql.getSQL(),
                sql.getParams()
            );

            return AjaxResult.success(getDataTable(paginate));
        } catch (Exception e) {
            logger.error("获取照片视频列表失败", e);
            return AjaxResult.error("获取列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取照片视频详情（仅公示的）
     */
    @GetMapping("/moment/{id}")
    @ResponseBody
    public AjaxResult getMomentDetail(@PathVariable("id") Long id) {
        try {
            // 检查用户登录状态
            if (getCurrentUser() == null) {
                return AjaxResult.error("用户未登录");
            }
            
            String communityId = getCurrentUser().getCommunityId();
            
            Record moment = Db.findFirst(
                "SELECT m.*, t.team_name, mem.nickname FROM eh_markicam_moment m " +
                "LEFT JOIN eh_markicam_team t ON m.team_id = t.team_id AND m.community_id = t.community_id " +
                "LEFT JOIN eh_markicam_member mem ON m.uid = mem.uid AND m.community_id = mem.community_id " +
                "WHERE m.id = ? AND m.community_id = ? AND m.is_deleted = 0 AND m.is_public = 1", 
                id, communityId);
                
            if (moment == null) {
                return AjaxResult.error("记录不存在或未公示");
            }
            
            return AjaxResult.success(moment);
        } catch (Exception e) {
            logger.error("获取照片视频详情失败", e);
            return AjaxResult.error("获取详情失败：" + e.getMessage());
        }
    }

    /**
     * 转换分页数据格式
     */
    private TableDataInfo getDataTable(Page<Record> page) {
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(0);
        rspData.setRows(page.getList());
        rspData.setTotal(page.getTotalRow());
        return rspData;
    }
}
